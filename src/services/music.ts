import { ApiService } from './request';
import type {
  ApiResponse,
  SubmitTrackRequest,
  SubmitTrackResponse,
  TracksByGenreResponse,
  RankedTracksResponse,
  ArtistTracksResponse,
  Genre,
  AudioFormat,
  ReleaseOption,
  API_ENDPOINTS,
} from '@/types/api';

/**
 * 音乐相关 API 服务
 */
export const musicApi = {
  /**
   * 提交音乐作品
   * @param data 音乐作品数据
   * @returns 提交结果
   */
  async submitTrack(
    data: SubmitTrackRequest
  ): Promise<ApiResponse<SubmitTrackResponse>> {
    return ApiService.post(API_ENDPOINTS.MUSIC.SUBMIT_TRACK, data);
  },

  /**
   * 获取按类型分组的最新音乐
   * @param numOfLines 每个类型的行数
   * @returns 按类型分组的音乐列表
   */
  async getTracksByGenre(
    numOfLines: number
  ): Promise<ApiResponse<TracksByGenreResponse>> {
    return ApiService.get(API_ENDPOINTS.MUSIC.TRACKS_BY_GENRE, {
      params: { numOfLines },
    });
  },

  /**
   * 获取按周期收入排名的音乐列表
   * @param pageNum 页码
   * @param pageSize 页面大小
   * @returns 按收入排名的音乐列表
   */
  async getTracksByRevenue(
    pageNum: number,
    pageSize: number
  ): Promise<ApiResponse<RankedTracksResponse>> {
    return ApiService.get(API_ENDPOINTS.MUSIC.TRACKS_BY_REVENUE, {
      params: { pageNum, pageSize },
    });
  },

  /**
   * 获取按周期播放量排名的音乐列表
   * @param pageNum 页码
   * @param pageSize 页面大小
   * @returns 按播放量排名的音乐列表
   */
  async getTracksByStreams(
    pageNum: number,
    pageSize: number
  ): Promise<ApiResponse<RankedTracksResponse>> {
    return ApiService.get(API_ENDPOINTS.MUSIC.TRACKS_BY_STREAMS, {
      params: { pageNum, pageSize },
    });
  },

  /**
   * 根据艺术家ID获取音乐列表
   * @param artistId 艺术家ID
   * @param pageNum 页码
   * @param pageSize 页面大小
   * @returns 艺术家的音乐列表
   */
  async getTracksByArtist(
    artistId: string,
    pageNum: number,
    pageSize: number
  ): Promise<ApiResponse<ArtistTracksResponse>> {
    return ApiService.get(
      `${API_ENDPOINTS.MUSIC.TRACKS_BY_ARTIST}/${artistId}/tracks`,
      {
        params: { pageNum, pageSize },
      }
    );
  },

  /**
   * 获取默认音乐类型列表
   * @returns 音乐类型列表
   */
  async getDefaultGenres(): Promise<ApiResponse<Genre[]>> {
    return ApiService.get(API_ENDPOINTS.META.DEFAULT_GENRES);
  },

  /**
   * 获取默认音频格式列表
   * @returns 音频格式列表
   */
  async getDefaultAudioFormats(): Promise<ApiResponse<AudioFormat[]>> {
    return ApiService.get(API_ENDPOINTS.META.DEFAULT_AUDIO_FORMATS);
  },

  /**
   * 获取默认发布选项列表
   * @returns 发布选项列表
   */
  async getDefaultReleaseOptions(): Promise<ApiResponse<ReleaseOption[]>> {
    return ApiService.get(API_ENDPOINTS.META.DEFAULT_RELEASE_OPTIONS);
  },
};

/**
 * 音乐相关工具函数
 */
export const musicUtils = {
  /**
   * 将分转换为美元
   * @param cents 分
   * @returns 美元
   */
  centsToUSD(cents: number): number {
    return cents / 100;
  },

  /**
   * 格式化收入显示
   * @param cents 分
   * @returns 格式化的美元字符串
   */
  formatRevenue(cents: number): string {
    const usd = this.centsToUSD(cents);
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(usd);
  },

  /**
   * 格式化播放量显示
   * @param streams 播放量
   * @returns 格式化的播放量字符串
   */
  formatStreams(streams: number): string {
    if (streams >= 1000000) {
      return `${(streams / 1000000).toFixed(1)}M`;
    } else if (streams >= 1000) {
      return `${(streams / 1000).toFixed(1)}K`;
    }
    return streams.toString();
  },

  /**
   * 格式化时间戳为日期字符串
   * @param timestamp 时间戳
   * @returns 格式化的日期字符串
   */
  formatDate(timestamp: number): string {
    return new Date(timestamp).toLocaleDateString('zh-CN');
  },

  /**
   * 验证音乐作品数据
   * @param data 音乐作品数据
   * @returns 验证结果
   */
  validateTrackData(data: Partial<SubmitTrackRequest>): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!data.title?.trim()) {
      errors.push('音乐标题不能为空');
    }

    if (!data.labelName?.trim()) {
      errors.push('唱片公司名称不能为空');
    }

    if (!data.albumName?.trim()) {
      errors.push('专辑名称不能为空');
    }

    if (!data.primaryLanguage?.trim()) {
      errors.push('主要语言不能为空');
    }

    if (!data.primaryGenreId?.trim()) {
      errors.push('主要音乐类型不能为空');
    }

    if (!data.originalReleaseDate) {
      errors.push('原始发布日期不能为空');
    }

    if (!data.streetDate) {
      errors.push('街头发布日期不能为空');
    }

    if (!data.copyrightName?.trim()) {
      errors.push('版权所有者名称不能为空');
    }

    if (!data.copyrightYear?.trim()) {
      errors.push('版权年份不能为空');
    }

    if (!data.phonogramCopyright?.trim()) {
      errors.push('录音版权所有者不能为空');
    }

    if (!data.phonogramCopyrightYear?.trim()) {
      errors.push('录音版权年份不能为空');
    }

    if (!data.coverArtUrl?.trim()) {
      errors.push('封面艺术URL不能为空');
    }

    if (!data.mediaUrls || data.mediaUrls.length === 0) {
      errors.push('至少需要一个媒体文件URL');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },
};

// 默认导出
export default {
  api: musicApi,
  utils: musicUtils,
};
