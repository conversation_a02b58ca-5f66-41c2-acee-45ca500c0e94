import React, { useState } from 'react';
import { Card, Button, Table, message, Pagination, Input, Space } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { api, musicUtils } from '@/services';
import type { Track, ArtistTracksResponse } from '@/types/api';

/**
 * 艺术家音乐列表示例组件
 */
const ArtistTracksExample: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [artistId, setArtistId] = useState('');
  const [tracks, setTracks] = useState<Track[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取艺术家音乐列表
  const fetchArtistTracks = async (
    id: string = artistId,
    page: number = 1,
    size: number = 10
  ) => {
    if (!id.trim()) {
      message.error('请输入艺术家ID');
      return;
    }

    try {
      setLoading(true);
      const response = await api.music.getTracksByArtist(id.trim(), page, size);
      
      if (response.code === 200) {
        const data: ArtistTracksResponse = response.body;
        setTracks(data.result);
        setPagination({
          current: data.pageNum,
          pageSize: data.pageSize,
          total: data.totalRows,
        });
      } else {
        message.error('获取艺术家音乐列表失败');
      }
    } catch (error) {
      message.error('获取艺术家音乐列表失败');
      console.error('Error fetching artist tracks:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理分页变化
  const handlePaginationChange = (page: number, pageSize?: number) => {
    fetchArtistTracks(artistId, page, pageSize || pagination.pageSize);
  };

  // 处理搜索
  const handleSearch = () => {
    fetchArtistTracks(artistId, 1, pagination.pageSize);
  };

  // 表格列定义
  const columns = [
    {
      title: '封面',
      dataIndex: 'coverArtUrl',
      key: 'coverArtUrl',
      width: 80,
      render: (url: string) => (
        <img 
          src={url} 
          alt="封面" 
          style={{ width: 50, height: 50, objectFit: 'cover', borderRadius: 4 }}
          onError={(e) => {
            (e.target as HTMLImageElement).src = '/placeholder-music.png';
          }}
        />
      ),
    },
    {
      title: '歌曲名称',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '艺术家',
      dataIndex: 'artistStageName',
      key: 'artistStageName',
    },
    {
      title: '类型',
      dataIndex: 'genre',
      key: 'genre',
    },
    {
      title: '周期收入',
      dataIndex: 'periodicRevenue',
      key: 'periodicRevenue',
      render: (value: number) => musicUtils.formatRevenue(value),
    },
    {
      title: '总收入',
      dataIndex: 'totalRevenue',
      key: 'totalRevenue',
      render: (value: number) => musicUtils.formatRevenue(value),
    },
    {
      title: '周期播放量',
      dataIndex: 'periodicStreams',
      key: 'periodicStreams',
      render: (value: number) => musicUtils.formatStreams(value),
    },
    {
      title: '总播放量',
      dataIndex: 'totalStreams',
      key: 'totalStreams',
      render: (value: number) => musicUtils.formatStreams(value),
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
      render: (timestamp: number | null) => 
        timestamp ? musicUtils.formatDate(timestamp) : '-',
    },
  ];

  return (
    <div className="p-6">
      <Card title="艺术家音乐列表查询" className="w-full">
        <div className="space-y-4">
          {/* 搜索区域 */}
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                艺术家ID
              </label>
              <Input
                placeholder="请输入艺术家ID，例如：e6286d4e-31a0-430a-9c8d-66c16f9c1fa0"
                value={artistId}
                onChange={(e) => setArtistId(e.target.value)}
                onPressEnter={handleSearch}
              />
            </div>
            <Button 
              type="primary" 
              icon={<SearchOutlined />}
              onClick={handleSearch}
              loading={loading}
            >
              查询
            </Button>
          </div>

          {/* 示例艺术家ID */}
          <div className="bg-gray-50 p-3 rounded">
            <p className="text-sm text-gray-600 mb-2">示例艺术家ID（点击可快速填入）：</p>
            <Space wrap>
              <Button 
                size="small" 
                type="link"
                onClick={() => setArtistId('e6286d4e-31a0-430a-9c8d-66c16f9c1fa0')}
              >
                e6286d4e-31a0-430a-9c8d-66c16f9c1fa0
              </Button>
            </Space>
          </div>

          {/* 音乐列表表格 */}
          {tracks.length > 0 && (
            <Card title="音乐列表" size="small">
              <Table
                columns={columns}
                dataSource={tracks}
                rowKey="id"
                loading={loading}
                pagination={false}
                scroll={{ x: 1000 }}
              />
              <div className="mt-4 flex justify-center">
                <Pagination
                  current={pagination.current}
                  pageSize={pagination.pageSize}
                  total={pagination.total}
                  onChange={handlePaginationChange}
                  showSizeChanger
                  showQuickJumper
                  showTotal={(total, range) => 
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                  }
                />
              </div>
            </Card>
          )}

          {/* 空状态 */}
          {!loading && tracks.length === 0 && artistId && (
            <div className="text-center py-8 text-gray-500">
              <p>未找到该艺术家的音乐作品</p>
              <p className="text-sm">请检查艺术家ID是否正确</p>
            </div>
          )}

          {/* 使用说明 */}
          <Card title="使用说明" size="small" className="mt-4">
            <div className="text-sm text-gray-600 space-y-2">
              <p>• 输入有效的艺术家ID来查询该艺术家的所有音乐作品</p>
              <p>• 支持分页查询，可以设置每页显示的数量</p>
              <p>• 显示音乐的基本信息、收入数据和播放量统计</p>
              <p>• 可以点击示例ID快速测试功能</p>
            </div>
          </Card>
        </div>
      </Card>
    </div>
  );
};

export default ArtistTracksExample;
