// API 相关类型定义

// ==================== 通用类型 ====================

// API 响应基础结构
export interface ApiResponse<T = any> {
  code: number;
  body: T;
  message?: string;
}

// API 错误响应
export interface ApiErrorResponse {
  code: number;
  message: string;
  details?: string;
}

// 角色类型
export interface Role {
  id: string;
  realm: number;
  createDate: string | null;
  lastUpdate: string | null;
  name: string;
  uiPermissions: string[] | null;
}

// 用户资料类型
export interface UserProfile {
  firstName: string;
  lastName: string;
  addressLine1?: string;
  addressLine2?: string;
  stateProvince?: string;
  countryCode?: string;
  postalZipCode?: string;
  avatarUrl?: string;
  stageName?: string;
  bio?: string;
}

// ==================== 认证相关类型 ====================

// 用户注册请求
export interface SignupRequest {
  username: string;
  password: string;
  alias: string;
  profile: UserProfile;
  defaultRoleId: string;
}

// 用户登录请求
export interface LoginRequest {
  username: string;
  password: string;
}

// 验证码登录请求
export interface OtpLoginRequest {
  username: string;
  verificationCode: string;
}

// 发送验证码请求
export interface SendOtpRequest {
  recipient: string;
}

// 验证OTP请求
export interface VerifyOtpRequest {
  username: string;
  verificationCode: string;
}

// 登录认证响应数据
export interface AuthResponse {
  token: string;
  accountId: string;
  realm: number;
  avatarUrl: string | null;
  displayName: string;
  alias: string;
  stageName: string | null;
  roles: Role[];
  uiPermissions: string[];
}

// 简单布尔响应
export interface BooleanResponse {
  trueOrFalse: boolean;
}

// 别名检查响<EditOutlined />应
export interface AliasCheckResponse {
  trueOrFalse: boolean;
  suggestions?: string[];
}

// ==================== 元数据相关类型 ====================

// 国家信息
export interface Country {
  code: string;
  name: string;
}

// 州/省信息
export interface Subdivision {
  code: string;
  name: string;
}

// 角色信息
export interface DefaultRole {
  code: string;
  name: string;
}

// ==================== 用户资料相关类型 ====================

// 用户资料响应
export interface UserProfileResponse {
  accountId: string | null;
  alias: string;
  email: string;
  mobile: string | null;
  firstName: string;
  lastName: string;
  addressLine1: string | null; // 街道
  addressLine2: string | null; // 城市
  stateProvince: string | null; // 州
  countryCode: string | null; // 国家
  postalZipCode: string | null; // 邮政编码
  avatarUrl: string | null;
  stageName: string | null; // 艺名
  bio: string | null; // 艺人简介
  displayName: string; // 显示名称
}

// 头像预签名URL响应
export interface AvatarPresignedUrlResponse {
  presignedUrl: string;
}

// 更新用户资料请求
export interface UpdateProfileRequest {
  alias?: string;
  email?: string;
  mobile?: string | null;
  displayName?: string;
  firstName?: string;
  lastName?: string;
  addressLine1?: string | null;
  addressLine2?: string | null;
  stateProvince?: string | null;
  countryCode?: string | null;
  postalZipCode?: string | null;
  avatarUrl?: string | null;
  stageName?: string | null;
  bio?: string | null;
}

// 修改密码请求
export interface ChangePasswordRequest {
  password: string;
}

// ==================== 音乐相关类型 ====================

// 音乐作品信息
export interface Track {
  id: string;
  title: string;
  artistId: string;
  artistStageName: string;
  genre: string;
  coverArtUrl: string;
  periodicRevenue: number; // 周期收入（分）
  totalRevenue: number; // 总收入（分）
  periodicStreams: number; // 周期播放量
  totalStreams: number; // 总播放量
  lastUpdate: number | null; // 最后更新时间戳
}

// 提交音乐作品请求
export interface SubmitTrackRequest {
  title: string;
  labelName: string;
  albumName: string;
  trackInfo?: string;
  primaryLanguage: string;
  upc?: string;
  isrc?: string;
  primaryGenreId: string;
  secondaryGenreId?: string;
  originalReleaseDate: number; // 时间戳
  streetDate: number; // 时间戳
  copyrightName: string;
  copyrightYear: string;
  phonogramCopyright: string;
  phonogramCopyrightYear: string;
  coverArtUrl: string;
  audioFormats?: string[];
  releaseOptions?: string[];
  mediaUrls: string[];
}

// 提交音乐作品响应
export interface SubmitTrackResponse {
  id: string;
}

// 按类型分组的音乐响应
export interface TracksByGenreResponse {
  [genre: string]: Track[];
}

// 分页视图
export interface PageView<T> {
  pageNum: number;
  pageSize: number;
  totalPages: number;
  totalRows: number;
  result: T[];
  empty: boolean;
}

// 排名音乐列表响应
export interface RankedTracksResponse {
  lastUpdate: number;
  pageView: PageView<Track>;
}

// 艺术家音乐列表响应（直接返回分页数据）
export interface ArtistTracksResponse extends PageView<Track> {}

// 音乐类型信息
export interface Genre {
  code: string;
  name: string;
}

// 音频格式信息
export interface AudioFormat {
  code: string;
  name: string;
}

// 发布选项信息
export interface ReleaseOption {
  code: string;
  name: string;
}

// ==================== 常量定义 ====================

// 默认角色ID
export const DEFAULT_ROLES = {
  INVESTOR: 'account.role.investor',
  ARTIST: 'account.role.artist',
} as const;

// API 端点
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    SIGNUP: '/auth/signup', // 用户注册
    LOGIN: '/auth/login', // 用户登录
    LOGIN_OTP: '/auth/login/otp', // 验证码登录
    SEND_SIGNUP_OTP: '/auth/otp/signup', // 发送注册验证码
    SEND_LOGIN_OTP: '/auth/otp/login', // 发送登录验证码
    SEND_CHANGE_USERNAME_OTP: '/auth/otp/change-username', // 发送更改用户名验证码
    VERIFY_OTP: '/auth/otp/verify', // 验证OTP验证码
    CHECK_USERNAME: '/auth/check-username', // 检查用户名可用性
    CHECK_ALIAS: '/auth/check-alias', // 检查别名可用性
  },
  // 用户资料相关
  USER: {
    PROFILE: '/member/profile', // 获取用户资料
    CHANGE_PASSWORD: '/member/profile/password', // 修改密码
    UPLOAD_AVATAR: '/member/profile/avatar', // 上传头像
  },
  // 元数据相关
  META: {
    COUNTRIES: '/meta/countries', // 获取所有国家列表
    COUNTRY_BY_CODE: '/meta/countries', // 根据国家代码获取国家信息
    SUBDIVISIONS: '/meta/countries', // 根据国家代码获取州/省列表
    SUBDIVISION_BY_CODE: '/meta/countries', // 根据国家代码和州/省代码获取州/省信息
    DEFAULT_ROLES: '/meta/default/member-roles', // 获取默认角色列表
    DEFAULT_ROLE_BY_ID: '/meta/default/member-roles', // 根据角色ID获取默认角色信息
    AVATAR_PRESIGNED_URL: '/meta/avatar/presigned-url', // 获取头像上传预签名URL
    DEFAULT_GENRES: '/meta/default/genres', // 获取默认音乐类型列表
    DEFAULT_AUDIO_FORMATS: '/meta/default/audio-formats', // 获取默认音频格式列表
    DEFAULT_RELEASE_OPTIONS: '/meta/default/release-options', // 获取默认发布选项列表
  },
  // 音乐相关
  MUSIC: {
    SUBMIT_TRACK: '/member/track', // 提交音乐作品
    TRACKS_BY_GENRE: '/home/<USER>/genres/tracks', // 按类型分组的最新音乐
    TRACKS_BY_REVENUE: '/home/<USER>/tracks/ranked-by-periodic-revenue', // 按周期收入排名的音乐
    TRACKS_BY_STREAMS: '/home/<USER>/tracks/ranked-by-periodic-streams', // 按周期播放量排名的音乐
    TRACKS_BY_ARTIST: '/home/<USER>/artists', // 根据艺术家ID获取音乐列表
  },
} as const;
